<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="HandheldFriendly" content="true" />

    <title>Harry Potter Story Intelligence Graph — Networks of characters & relationships</title>
    <meta name="title" content="Harry Potter Story Intelligence Graph — Networks of characters & relationships" />
    <meta name="application-name" content="Harry Potter Story Intelligence Graph">
    <meta name="description" content="Visualize network maps of Harry Potter characters and their relationships across chapters. Explore character interactions and story development." />
    <meta name="language" content="en" />
    <meta name="robots" content="index, follow" />

    <link rel="canonical" href="https://www.marvel-graphs.net/" />
    <link rel="icon" sizes="16x16 24x24 32x32 48x48" href="./favicon.ico" type="image/x-icon" />
    <link rel="shortcut icon" sizes="196x196" href="./favicon-196.png" type="image/png" />
    <link rel="apple-touch-icon" sizes="180x180" href="./apple-touch-icon.png" />

    <link rel="stylesheet" href="./style.css" />

    <meta property="og:title" content="Harry Potter Story Intelligence Graph" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="Harry Potter Story Intelligence Graph" />
    <meta property="og:url" content="#" />
    <meta property="og:description" content="Visualize network maps of Harry Potter characters and their relationships through chapter-by-chapter analysis of the story." />
    <meta property="og:image" content="./images/screenshot.png" />
    <meta property="og:image:type" content="image/png" />
    <meta property="og:locale" content="en_US" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@boogheta" />
    <meta name="twitter:creator" content="@boogheta" />
    <meta name="twitter:title" content="Harry Potter Story Intelligence Graph" />
    <meta name="twitter:description" content="Visualize network maps of Harry Potter characters and their relationships through chapter-by-chapter analysis of the story." />
    <meta name="twitter:image" content="./images/screenshot.png" />
  </head>
  <body>
    <div id="rotate-modal">
      <img class="logo" src="./images/logo.png"/>
      <h2><span class="marvel">HARRY POTTER </span>Story Intelligence Graph</h2>
      <img id="rotate-modal-img" src="./images/smartphone.png" />
      <p>Please rotate!<br/>
        <small>Note this app works on mobiles but is way nicer on bigger screens</small>
      </p>
    </div>
    <div id="tooltip"></div>
    <div id="sidebar">
      <div id="header">
        <img class="reset-graph logo" src="./images/logo.png"/>
        <h1 class="reset-graph"><span class="marvel">HARRY POTTER </span><span>Story Intelligence Graph</span></h1>
        <h2>
          <span class="hide-small">Here is a map of</span>
          <span class="show-middle">Map of</span>
          <span id="title"></span><br/>
          <span id="node-label" class="red"></span>
        </h2>
      </div>
      <div id="explanations" class="sidebar-text">
        <p>This graph shows a map of <b id="order"></b> Harry Potter <span class="entity"></span> positioned close to those they have the strongest relationships with.</p>
        <p>The network includes all <span class="entity"></span> <span class="characters-details">(including magical creatures and entities)</span> mentioned in the analyzed chapters from Harry Potter and the Deathly Hallows. Characters with minimal interactions (fewer than <span id="cooccurrence-threshold"></span> relationship instances) were filtered out to focus on the most significant relationships.</p>
        <p>The size of the nodes reflects how often the <span class="entity"></span> appear across different chapters and their importance to the story.</p>
        <p><span class="characters-details">Colors indicate which <i>community</i> each character belongs to: <span id="clusters-legend"></span>, etc.</span></p>
        <p>The data is extracted from detailed chapter analysis, capturing character interactions, relationships, and emotional dynamics throughout the story.</p>
        <p id="view-node" class="button tooltip selected" tooltip="focus a random character in the graph"><span><span class="desktop-details">Click</span><span class="mobiles-details">Tap</span> a <span class="characters-details">character</span></span></p>
      </div>
      <div id="node-details" class="sidebar-text">
        <img id="node-img"/>
        <div id="node-extra"></div>
      </div>
      <div id="choices" class="selected">
        <h5>Chapter View</h5>
        <div class="chapter-selector">
          <select id="chapter-select" class="tooltip" tooltip="Select a chapter to view its character network">
            <option value="all">All Chapters (Global View)</option>
            <option value="2">Chapter 2: In Memoriam</option>
            <option value="3">Chapter 3: The Dursleys Departing</option>
            <option value="4">Chapter 4: The Seven Potters</option>
          </select>
        </div>
        <div id="chapter-info" class="chapter-details">
          <h6 id="chapter-title"></h6>
          <p id="chapter-mood"></p>
          <div id="chapter-themes"></div>
        </div>
      </div>
      <div id="old-choices" style="display: none;">
        <!-- Hidden for Harry Potter - only characters available -->
        <h5>Switch to the graph of</h5>
        <div class="network-choice">
          <input type="checkbox" id="node-type-switch" />
          <label id="switch-type" class="network-switch-label tooltip" for="node-type-switch">
            <div class="toggle"></div>
            <div class="names">
              <div id="type-characters" class="left">characters</div>
              <div id="type-creators" class="right">creators</div>
            </div>
          </label>
        </div>
      </div>
      <div id="credits">
        <div id="help" class="tooltip" tooltip="read more info &amp;&nbsp;explanations">
          <span>?</span>
        </div>
        <p>data <span class="large-bar">extracted </span>from <a href="https://www.jkrowling.com/" target="_blank">Harry Potter</a> books</p>
        <p>
          <span class="large-bar">built </span>by <a href="https://twitter.com/boogheta" target="_blank">@boogheta</a>
          <b class="large-bar">⋅</b>
          <span class="large-bar">code </span>on <a href="https://github.com/boogheta/Marvel#readme" target="_blank">GitHub</a>
        </p>
      </div>
    </div>
    <noscript id="noscript"><p>This app runs with JavaScript.<br/>Please enable it to continue.</p></noscript>
    <div id="webgl-disclaimer" class="sigma-container">
      <p>Sorry, this app only works on modern browsers with WebGL capabilities :(</p>
    </div>
    <div id="sigma-container" class="sigma-container"></div>
    <div id="controls">
      <div id="sigma-buttons">
        <button id="regscreen" class="tooltip" tooltip="reduce to window">
          <img src="./images/exit-fullscreen.svg" alt="exit-fullscreen" />
        </button>
        <button id="fullscreen" class="tooltip" tooltip="maximize to full screen">
          <img src="./images/fullscreen.svg" alt="fullscreen" />
        </button>
        <button id="zoom-out" class="tooltip" tooltip="zoom out">
          <img src="./images/zoom-out.svg" alt="zoom out" />
        </button>
        <button id="zoom-in" class="tooltip" tooltip="zoom in">
          <img src="./images/zoom-in.svg" alt="zoom in" />
        </button>
        <button id="zoom-reset" class="tooltip" tooltip="recenter &amp; reset zoom">
          <img src="./images/reset-zoom.svg" alt="reset-zoom" />
        </button>
      </div>
      <div id="search">
        <input type="search" id="search-input" list="suggestions" placeholder="search…" class="tooltip" tooltip="search a specific 'entity' in the graph" />
        <img id="search-icon" src="./images/search.svg" alt="search a node in the graph" />
        <datalist id="suggestions"><option>&nbsp;</option></datalist>
        <select id="suggestions-select"><option>search…</option></select>
      </div>
    </div>
    <div id="legend">
      <p><span class="desktop-details">click</span><span class="mobiles-details">tap</span> on a character to select them and explore their relationships, details&nbsp;and&nbsp;chapter&nbsp;appearances</p>
    </div>
    <div id="histogram-container">
      <div id="histogram-title">
        <span id="comics-title"></span>
      </div>
      <div id="histogram"></div>
      <div id="histogram-hover"></div>
      <div id="histogram-legend"></div>
      <div id="histogram-tooltip"></div>
    </div>
    <div id="view-comics-container">
      <p id="view-comics" class="button tooltip selected">
        <span>
          Explore <span class="hide-small">the </span>chapters
          <img src="./images/left.svg" alt="open chapters list" />
        </span>
      </p>
    </div>
    <div id="comics-bar">
      <div id="comics-header">
        <div id="comics-actions">
          <button id="comics-filter" class="tooltip" tooltip="search chapters">
            <img src="./images/search.svg" alt="filter chapters" />
          </button>
          <button id="comics-sort-date" class="sort-button tooltip selected">
            <img src="./images/sort-date.svg" alt="sort chronologically" />
          </button>
          <button id="comics-sort-alpha" class="sort-button tooltip">
            <img src="./images/sort-alpha.svg" alt="sort alphabetically" />
          </button>
          <button id="comics-prev" class="tooltip" tooltip="previous chapter">
            <img src="./images/left.svg" alt="previous-picture" />
          </button>
          <button id="comics-play" class="tooltip" tooltip="start scrolling chapters">
            <img src="./images/play.svg" alt="play" />
          </button>
          <button id="comics-pause" class="selected tooltip" tooltip="stop scrolling chapters">
            <img src="./images/pause.svg" alt="pause" />
          </button>
          <button id="comics-next" class="tooltip" tooltip="next chapter">
            <img src="./images/right.svg" alt="previous-picture" />
          </button>
          <button id="close-bar" class="tooltip" tooltip="close list of&nbsp;chapters">
            <img src="./images/close.svg" alt="close-chapters-list" />
          </button>
        </div>
        <form id="filter-comics" autocomplete="off">
          <input type="search" id="filter-input" placeholder="search…" class="tooltip" tooltip="filter chapters by title" />
        </form>
      </div>
      <span id="comics-subtitle"></span>
      <div id="comics">
        <ul id="comics-list"></ul>
        <div id="loader-list" class="tooltip" tooltip="loading list of chapters…"><img src="./images/loader.gif"/></div>
      </div>
      <div id="comics-cache"></div>
      <div id="comic-details">
        <img id="comic-img"/>
        <h4 id="comic-title"></h4>
        <div>
          <p id="comic-desc"></p>
          <div class="comic-entities">
            <h4>key themes:</h4>
            <ul id="comic-creators"></ul>
          </div>
          <div class="comic-entities">
            <h4>featuring:</h4>
            <ul id="comic-characters"></ul>
          </div>
        </div>
      </div>
    </div>
    <div id="loader" class="loader tooltip" tooltip="loading network…"><img src="./images/loader.gif"/></div>
    <div id="loader-comics" class="loader tooltip" tooltip="loading chapters…">
      <img src="./images/loader.gif"/>
      <span>loading chapters...</span>
    </div>
    <div id="modal-img-missing">image missing</div>
    <div id="modal" class="modal">
      <span id="modal-helper"></span>
      <img id="modal-img"/>
      <div id="modal-actions">
        <button id="modal-previous" class="tooltip" tooltip="previous comic">
          <img src="./images/left.svg" alt="previous-picture" />
        </button>
        <button id="modal-play" class="tooltip" tooltip="start scrolling comics">
          <img src="./images/play.svg" alt="play" />
        </button>
        <button id="modal-pause" class="selected tooltip" tooltip="stop scrolling comics">
          <img src="./images/pause.svg" alt="pause" />
        </button>
        <button id="modal-next" class="tooltip" tooltip="next comic">
          <img src="./images/right.svg" alt="previous-picture" />
        </button>
        <button id="close-modal" class="close-button tooltip" tooltip="close picture overlay">
          <img src="./images/close.svg" alt="close-picture-modal" />
        </button>
      </div>
    </div>
    <div id="help-modal" class="modal">
      <div id="help-box">
        <p><b>Harry Potter Story Intelligence Graph</b> is an app that displays network maps of Harry Potter characters where each one is positioned close to those they have the strongest relationships with based on chapter-by-chapter analysis of the story.</p>
        <p>Character data and relationships are extracted from Harry Potter books using AI analysis to identify interactions, emotional connections, and story significance.</p>
        <br/>
        <p>The data represents character relationships and interactions as they appear in the analyzed chapters. While the AI extraction aims for accuracy, the relationships should be considered as interpretive analysis of the text rather than definitive canonical information.</p>
        <p>You can explore individual chapters to see how relationships develop throughout the story, or view the global network to see overall character connections across all analyzed chapters.</p>
        <br/>
        <p>The nodes positions are computed using the <a href="https://journals.plos.org/plosone/article?id=10.1371/journal.pone.0098679" target="_blank">ForceAtlas2 algorithm</a> implemented in <a href="https://doi.org/10.1371/journal.pone.0098679" target="_blank">graphology.js</a>, which places nodes that are the most linked together closer and further from the others.</p>
        <p>The characters <i>families</i> used for the colors are network communities that were manually labeled after being computed with graphology's <a href="https://en.wikipedia.org/wiki/Louvain_method" target="_blank">Louvain algorithm</a> <a href="https://graphology.github.io/standard-library/communities-louvain" target="_blank">implementation</a>. They are therefore sometimes a little imprecise.</p>
        <br/>
        <p>All views have their own URL so each one can be shared directly with a link.</p>
        <p>The app should work on most mobile devices (and <a href="https://sigmajs.org/" target="_blank">Sigma</a>'s dual-touch interactions with the graphs are quite cool!) but exploring the graphs and comics is way nicer on bigger screens.</p>
        <p>You can <i>"install"</i> it as a mobile app by simply clicking the "Add to home screen" option within your device's browser options menu.</p>
        <br/>
        <p>This web application is entirely built with Free Libre Open Source Software and released as such under the <a href="https://github.com/boogheta/Marvel/blob/master/LICENSE" target="_blank">AGPL V3 license</a>.</p>
        <p>The full source code is available on <a href="https://github.com/boogheta/Marvel/" target="_blank">GitHub</a>. <a href="https://github.com/boogheta/Marvel/issues" target="_blank">Issues and pull requests</a> welcome!</p>
        <br/>
        <p>Data collection and preparation in Python with <a href="https://requests.readthedocs.io/" target="_blank">requests</a> and <a href="https://networkx.org/" target="_blank">NetworkX</a>.</p>
        <p>Web interface and network visualization in Node.js using <a href="https://graphology.github.io/" target="_blank">graphology</a>, <a href="https://sigmajs.org/" target="_blank">Sigma.js</a>, <a href="http://nodeca.github.io/pako/" target="_blank">pako</a> and <a href="https://www.papaparse.com/" target="_blank">PapaParse</a>, built in TypeScript with <a href="https://www.npmjs.com/package/kotatsu" target="_blank">kotatsu</a>.</p>
        <p>All icons are used or adapted from SVG creations under CC Zero Public Domain by <a href="https://openclipart.org/detail/191399/smartphone" target="_blank">agomjo</a>, CC Attribution License by <a href="https://github.com/atisawd/boxicons" target="_blank">boxicons</a> and MIT license from <a href="https://github.com/artcoholic/akar-icons" target="_blank">artcoholic</a> &amp; <a href="https://github.com/twbs/icons" target="_blank">Bootstrap</a>.</p>
        <br/>
        <p>Thanks to <a href="https://www.jkrowling.com/" target="_blank">J.K. Rowling</a> for creating the incredible world of Harry Potter and the rich characters that make this analysis possible. The Harry Potter series continues to inspire readers and provide endless material for literary analysis and exploration.</p>
        <p>Thanks to <a href="https://github.com/Yomguithereal" target="_blank">Guillaume Plique</a>, <a href="https://github.com/paulgirard" target="_blank">Paul Girard</a>, <a href="https://github.com/jacomyal" target="_blank">Alexis Jacomy</a>, <a href="https://github.com/jacomyMa" target="_blank">Mathieu Jacomy</a> and <a href="https://github.com/robindemourat" target="_blank">Robin de Mourat</a> for their precious help, ideas and code libraries!</p>
        <p>And lots of thanks to the 2014 Amsterdam <a href="http://contropedia.net/" target="_blank">Contropedia</a> datasprint where this whole idea germinated before it got buried in my mind and GitHub repositories for 8 long years. :)</p>
        <br/>
        <p>If you're interested in literary analysis and network visualization, this project demonstrates how AI can be used to extract and visualize character relationships from narrative text, providing new ways to understand story structure and character development.</p>
        <br/>
        <p>You can find me on Mastodon:&nbsp;<a href="https://piaille.fr/@boogheta" target="_blank">@<EMAIL></a>, Twitter:&nbsp;<a href="https://twitter.com/boogheta" target="_blank">@boogheta</a> or&nbsp;<a href="https://github.com/boogheta">GitHub</a>.</p>
      </div>
      <button id="close-help" class="close-button tooltip" tooltip="close info box">
        <img src="./images/close.svg" alt="close-help-box" />
      </button>
    </div>
    <script src="build/bundle.js"></script>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-WX3GLGZ95V"></script>
    <script>
window.dataLayer = window.dataLayer || [];
function gtag() { dataLayer.push(arguments); };
gtag('js', new Date());
gtag('config', 'G-WX3GLGZ95V');
window.addEventListener("hashchange", function(event) {
   gtag('event', 'page_view', {
     'page_path': location.pathname + location.search + location.hash
   });
});
    </script>
  </body>
</html>
